Params	
	Numeric NT(30);//开仓区间周期参数，可转债波动相对较小，缩短周期
	Numeric RS(15);//默认出场参数，可转债止损可以更紧
	Numeric M(0.5); //出场自适应参数，调整幅度减小
	Numeric X(2);//自适应参数的步长，减小调整幅度
	Numeric Fund(100000);//可转债资金量
	Numeric MinLots(1);//最小交易手数，可转债1手=10股
Vars
	Numeric SA(0.3); //可转债ATR倍数调小
	Series<Numeric> ATRMD;
	Series<Numeric> ATRZZ;
	Series<Numeric> TR;
	Series<Numeric> ATR;
	Series<Numeric> ATRN;
	Series<Numeric> HH;
	Series<Numeric> LL;
	Numeric XY;
	Numeric SY;
	Numeric mids;
	Series<Numeric> TRS;//跟踪止损 
	Series<Numeric> Myprice2;
	Series<Numeric> Myprice3;
	Series<Numeric> liQKA;
	Series<Numeric> DliqPoint;
	Series<Numeric> KliqPoint;
	Series<Numeric> HighAfterEntry;
	Series<Numeric> LowAfterEntry;
	Series<Numeric> barcoutN;
	Series<Numeric> barN;
	Series<Numeric> NN;
	Numeric Length(14);
	Numeric Length2(20); 
	Numeric S;
	Series<Numeric> HHD;
	Series<Numeric> LLD;
	Series<Numeric> MAHD;
	Series<Numeric> MALD;
	Series<Numeric> CD;
	Series<Numeric> MHCD;
	Series<Numeric> MLCD;
	Series<Numeric> H1;
	Series<Numeric> L1;
	Series<Bool> DD;
	Series<Bool> KK;
	Series<Bool> ZD;
	Series<Numeric> VIX;
	Series<Numeric> VIX_index; 
	Series<Numeric> Lots;
	Series<Numeric> Ma1;
	Series<Numeric> Ma2;
	Series<Numeric> PremiumRate; //溢价率
	Series<Numeric> ConversionValue; //转换价值
Events
	OnInit()
	{
		//可转债不需要设置期货相关参数
		//AddDataFlag(Enum_Data_RolloverBackWard());	
		//AddDataFlag(Enum_Data_RolloverRealPrice());	
		//AddDataFlag(Enum_Data_AutoSwapPosition());	
		//AddDataFlag(Enum_Data_IgnoreSwapSignalCalc());	
	}
    onBar(ArrayRef<Integer> indexs)
    {    	
		Ma1=XAverage(C,60);  //可转债使用较短均线
		Ma2=XAverage(C,20);  //短期均线
		PlotNumeric("Ma1",Ma1);
		PlotNumeric("Ma2",Ma2);
		
		//可转债手数计算：1手=10股，最小交易单位
		Lots=Max(MinLots,IntPart(Fund/(Close*10*0.1)));	//计算开仓手数，10股为1手
		
		if(CurrentBar==0)
    	{
			NN=NT;
    		TRS=RS;
    		barN=0;
			S=SA;
    	}
    		//记录开仓后高低点
        If(BarsSinceentry == 0)
        {
            HighAfterEntry = High;
            LowAfterEntry = Low;
        }else
        {
            HighAfterEntry = Min(HighAfterEntry,High); // 空头止损，更新最低的最高价
            LowAfterEntry = Max(LowAfterEntry,Low);    // 多头止损，更新最高的最低价
        }

    	TR=MAX(MAX((HIGH-LOW),ABS(CLOSE[1]-HIGH)),ABS(CLOSE[1]-LOW));   
    	ATR=Average(TR,Length);
    	ATRMD=(ATR/Close)/Summation((ATR/Close),Length2);//计算一定范围的波动率
    	ATRN=ATRMD*100;//百分比化
    	
    	//可转债波动率阈值调整，相对股指期货更小
    	XY=0.02*100; //可转债波动率下限2%
    	SY=0.04*100; //可转债波动率上限4%
    	mids=0.03*100;//可转债波动率中线3%
    	
    	//PlotNumeric("ATRN",ATRN);
    	//PlotNumeric("XY",XY);
    	//PlotNumeric("SY",SY);
    	//PlotNumeric("mids",mids);

    	if(CurrentBar>barN)
    	{
			If(MarketPosition<>0)
			{//持仓波动率调整
				if(ATRN[1]>SY) //当波动率大于上限时，波动率逐步走向过热
				{
					if (ATRN[1]>ATRN[2])//波动率持续放大
					{
						TRS=TRS-M;
						TRS=Max(TRS,3); //可转债最小止损调整为3
					}
				}
				if(ATRN[1]<XY)//当波动率小于下限时，波动率逐步走向收缩。
				{	
					 if(ATRN[1]<ATRN[2])//波动率持续收缩
					{
						TRS=TRS+M;
						TRS=Min(TRS,15); //可转债最大止损调整为15
					}
				}
			}
			//开仓波动率调节
			if(ATRN[1]>SY) //当波动率大于上限时
			{
				if (ATRN[1]>ATRN[2])
				{
					NN=NN-3; //可转债调整幅度减小
					NN=Max(NN,10);		
					S=S-X;
					S=MAX(S,0.5); //可转债最小ATR倍数
				}
			}
			if(ATRN[1]<XY)//当波动率小于下限时
			{	
				 if(ATRN[1]<ATRN[2])
				{
					NN=NN+3;
					NN=Min(NN,60); //可转债最大周期	
					S=S+X;
					S=MIN(S,10); //可转债最大ATR倍数
				}
			}
		
			BarN=CurrentBar;
		}
    	Commentary("TRS"+Text(TRS));   
    	Commentary("NN"+Text(NN));  
    	Commentary("ATR"+Text(ATR));  		
    	
    	HH=Highest(H,NN)+ATR*S;
		LL=Lowest(L,NN)-ATR*S;
		PlotNumeric("HH",HH);
		PlotNumeric("LL",LL);

		//可转债开仓条件：价格突破且在均线之上，同时考虑双均线
		if (H>=HH[1] and MarketPosition==0 and Close[1]>Ma1[1] and Ma2[1]>Ma1[1]) 
		{
			buy(Lots,max(open,HH[1]));
			LowAfterEntry = max(open,HH[1]);//保存多头开仓价格;
			NN=NT;
			S=SA;
		}
		
		//可转债也可以做空，但条件更严格
		if(L<=LL[1] and MarketPosition==0 and Close[1]<Ma1[1] and Ma2[1]<Ma1[1]) 
		{
			SellShort(Lots,min(open,LL[1]));
			HighAfterEntry = min(open,LL[1]);//保存空头开仓价格;
			NN=NT;
			S=SA;
		}

		//跳空开仓
		if (open>=HH[1] and open[1]<Close[1] and MarketPosition==0 and Close[1]>Ma1[1] and Ma2[1]>Ma1[1]) 
		{
			buy(Lots,open);
			LowAfterEntry = open;//保存多头开仓价格;
			NN=NT;
			S=SA;
		}
		
		if(open<=LL[1] and open[1]>Close[1] and MarketPosition==0 and Close[1]<Ma1[1] and Ma2[1]<Ma1[1]) 
		{
			SellShort(Lots,open);
			HighAfterEntry = open;//保存空头开仓价格;
			NN=NT;
			S=SA;
		}		

		Commentary("barcoutN"+text(barcoutN));
		Commentary("BarsSinceEntry"+text(BarsSinceEntry));
    	If(MarketPosition == 0)   // 自适应参数默认值；
    	{
    		liQKA = 1;
    		barcoutN=0;
    	}Else if(BarsSinceEntry>barcoutN)					 
    	{
    		liQKA = liQKA - 0.05; //可转债衰减速度减慢
    		liQKA = Max(liQKA,0.4); //可转债最小衰减系数提高
    		barcoutN=BarsSinceEntry;
    	}
    	if(MarketPosition>0)
    	{
    	DliqPoint = LowAfterEntry - (Open*TRS/1000)*liQKA; 
    	}
    	if(MarketPosition<0)
    	{
    	KliqPoint = HighAfterEntry + (Open*TRS/1000)*liQKA; 
    	}
    	// 画线
    	Commentary("(Open*TRS/1000)*liQKA"+text((Open*TRS/1000)*liQKA));

    	// 持有多单时
     	If(MarketPosition >0 And BarsSinceEntry >0  And Low <= DliqPoint[1] and DliqPoint[1]>0 ) 
    	{
    		Sell(0,Min(Open,DliqPoint[1]));
    		barcoutN=0;
			TRS=RS;
    	}
    		// 持有空单时
    	If(MarketPosition <0 And BarsSinceEntry >0  And High >= KliqPoint[1] and KliqPoint[1]>0 )
    	{		
    		BuyToCover(0,Max(Open,KliqPoint[1]));
    		barcoutN=0;
			TRS=RS;
    	}	
   
}

//------------------------------------------------------------------------
// 编译版本	GS2015.12.25
// 可转债策略版本	2024/08/03
// 基于if_vix策略修改，适配可转债交易特点
// 主要修改：
// 1. 交易单位改为10股1手
// 2. 波动率阈值降低，适应可转债较小波动
// 3. 增加双均线过滤条件
// 4. 调整参数范围，适应可转债特性
// 5. 保留双向交易能力
//------------------------------------------------------------------------
